package com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 输出响应
 * <AUTHOR>
 * @description
 * @date 2025/5/9
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OutputResponse {

    /**
     * 当前状态
     */
    @JsonProperty("cur_state")
    private String curtState;

    /**
     * 结果,包含agentId或者不回复
     */
    @JsonProperty("final_result")
    private String result;

    /**
     * 调用方式
     * @see com.jd.qf.ai.server.common.pojo.enums.InvokeTypeEnum
     */
    @JsonProperty("invoke_type")
    private String invokeType;
}
