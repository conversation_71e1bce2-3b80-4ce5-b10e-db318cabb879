package com.jd.qf.ai.server.infrastructure.rpc.selfBuild;

import com.alibaba.fastjson.JSON;
import com.jd.qf.ai.server.infrastructure.rpc.common.BaseConfigService;
import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto.IntentReconRequest;
import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto.IntentReconResponse;
import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto.SelfBuildStreamRequest;
import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto.SelfBuildStreamResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * 意图识别RPC服务
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
@Slf4j
@Service
public class SelfBuildAgentRpcServiceImpl implements SelfBuildAgentRpcService {

    @Autowired
    private BaseConfigService baseConfigService;

    /**
     * 意图识别web客户端
     */
    private final WebClient intentWebClient;

    /**
     * 聊天web客户端
     */
    private final WebClient chatWebClient;

    public SelfBuildAgentRpcServiceImpl(@Value("${selfBuild.api.route.base-url}") String routeBaseUrl,
                                        @Value("${selfBuild.api.chat.base-url}") String chatBaseUrl) {
        this.intentWebClient = WebClient.builder()
                .baseUrl(routeBaseUrl)
                .build();

        this.chatWebClient = WebClient.builder()
                .baseUrl(chatBaseUrl)
                .build();
    }

    @Override
    public Mono<IntentReconResponse> intent(IntentReconRequest request) {

        log.info("通用意图识别接口入参: {}", JSON.toJSONString(request));

        //设置经过负载均衡的apikey
        request.getInput().put("api_key",baseConfigService.routeChatRhinoKey());

        return intentWebClient
                .post()
                .uri("batch/router/invoke")
                .contentType(MediaType.APPLICATION_JSON)
                .header("Accept-Charset", "UTF-8")
                .bodyValue(request)
                .retrieve()
                .bodyToMono(IntentReconResponse.class)
                .doOnSuccess(resp -> log.info("意图识别RPC返回{}", JSON.toJSONString(resp)))
                .onErrorResume(e -> {
                    log.error("意图识别RPC异常", e);
                    return Mono.empty();
                });
    }

    @Override
    public Flux<SelfBuildStreamResponse> streamChat(SelfBuildStreamRequest request) {

        log.info("自建流式聊天RPC入参: {}", JSON.toJSONString(request));

        //设置经过负载均衡的apikey
        request.getInput().put("api_key",baseConfigService.routeChatRhinoKey());

        return chatWebClient
                .post()
                .uri("/"+request.getAgentId()+"/stream")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.TEXT_EVENT_STREAM)
                .header("Accept-Charset", "UTF-8")
                .bodyValue(request)
                .retrieve()
                .bodyToFlux(String.class)
                //过滤掉结束数据块
                .filter(line -> !line.startsWith("end"))
                .map(line -> JSON.parseObject(line, SelfBuildStreamResponse.class))
                .onErrorResume(e -> {
                    log.error("自建流式聊天RPC异常", e);
                    return Flux.empty();
                });
    }


}
