package com.jd.qf.ai.agent.core.service.intent;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jd.qf.ai.agent.core.api.intent.IntentService;
import com.jd.qf.ai.agent.core.api.intent.bo.IntentBo;
import com.jd.qf.ai.agent.core.api.intent.bo.IntentCommand;
import com.jd.qf.ai.server.common.pojo.enums.IntentTypeEnum;
import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.SelfBuildAgentRpcService;
import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto.IntentReconRequest;
import com.jd.qf.ai.server.infrastructure.rpc.selfBuild.dto.OutputResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

import static com.jd.qf.ai.server.common.pojo.constants.ParamConstants.USER_INPUT;

/**
 * 意图识别服务
 *
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
@Slf4j
@Service
public class IntentServiceImpl implements IntentService {

    @Autowired
    private SelfBuildAgentRpcService selfBuildAgentRpcService;

    @Override
    public Mono<IntentBo> intent(IntentCommand request) {

        if (CollectionUtil.isEmpty(request.getMessageList())) {
            log.error("意图识别请求,入参messageList为空,入参为{}", JSON.toJSONString(request));
            return Mono.just(IntentBo.builder().intentType(IntentTypeEnum.NO_REPLY.getCode()).build());
        }

        IntentReconRequest reconRequest = new IntentReconRequest();
        Map<String, Object> input = new HashMap<>();
        input.put(USER_INPUT,request.getMessageList());
        input.putAll(request.getParams());
        reconRequest.setInput(input);
        return selfBuildAgentRpcService.intent(reconRequest)
                .map(resp -> {
                    OutputResponse output = resp.getOutput();

                    IntentBo response = new IntentBo();
                    response.setInvokeType(output.getInvokeType());
                    if (IntentTypeEnum.NO_REPLY.getCode().equals(output.getResult())) {
                        if (IntentTypeEnum.END.getCode().equals(output.getCurtState())) {
                            response.setIntentType(IntentTypeEnum.END.getCode());
                        } else {
                            response.setIntentType(IntentTypeEnum.NO_REPLY.getCode());
                        }
                    } else {
                        response.setIntentType(IntentTypeEnum.REPLY.getCode());
                        response.setAgentId(output.getResult());
                    }
                    return response;
                });
    }
}
