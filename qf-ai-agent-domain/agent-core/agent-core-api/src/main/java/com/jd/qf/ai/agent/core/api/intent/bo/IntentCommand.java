package com.jd.qf.ai.agent.core.api.intent.bo;

import com.jd.qf.ai.server.common.pojo.dto.CommonMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 通用意图识别请求
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IntentCommand {

    /**
     * 消息列表
     */
    List<CommonMessage> messageList;

    /**
     * 业务参数
     */
    Map<String, Object> params;

}
