package com.jd.qf.ai.biz.facade.controller.chat;

import com.jd.qf.ai.biz.common.dto.ScrmUserInfo;
import com.jd.qf.ai.biz.common.utils.ScrmLoginUtil;
import com.jd.qf.ai.biz.core.api.chat.ScrmAiChatService;
import com.jd.qf.ai.biz.core.api.chat.bo.*;
import com.jd.qf.ai.biz.facade.controller.chat.vo.req.*;
import com.jd.qf.ai.biz.facade.controller.chat.vo.resp.AiChatRecordResp;
import com.jd.qf.ai.biz.facade.controller.chat.vo.resp.IntentResp;
import com.jd.qf.ai.biz.facade.controller.chat.vo.resp.StreamChatResp;
import com.jd.qf.ai.biz.facade.controller.chat.vo.resp.UnReadAiRecordResp;
import com.jd.qf.ai.biz.facade.converter.Converter;
import com.jd.qf.ai.server.common.pojo.enums.dify.RequestTypeEnum;
import com.jd.qf.ai.server.common.pojo.resp.BizResponse;
import com.jdt.open.capability.client.redis.OpenRedisClient;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * SCRM AI 聊天控制器
 * <AUTHOR>
 * @description
 * @date 2025/4/2
 */
@RestController
@RequestMapping("/qf/ai/biz/scrm/chat")
@Slf4j
public class ScrmAiChatController {

    @Autowired
    private ScrmAiChatService scrmAiChatService;
    @Autowired
    private OpenRedisClient openRedisClient;

    /**
     * 意图识别
     */
    @RequestMapping("/intent")
    @ApiOperation("[2.16][杨佳润]客服工作台侧边栏意图识别")
    public Mono<BizResponse<IntentResp>> intent(HttpServletRequest request, @RequestBody IntentReq req) {

        IntentQuery query = Converter.INSTANCE.to(req);
        ScrmUserInfo userInfo = ScrmLoginUtil.getAssignUserFromHeader(request);
        query.setCorpId(userInfo.getCropId());
        query.setProjectId(userInfo.getProjectId());
        query.setRequestType(RequestTypeEnum.FRONTEND.getCode());
        return scrmAiChatService.intent(query)
                .map(resp-> BizResponse.success(Converter.INSTANCE.to(resp)));
    }

    /**
     * 异步请求AI回复
     * 已废弃,改为从JMQ监听消息
     */
    @Deprecated
    @RequestMapping("/asyncReply")
    @ApiOperation("[2.16][杨佳润]客服工作台侧边栏异步请求AI回复")
    public BizResponse<Void> asyncReply(HttpServletRequest request,@RequestBody AsyncReplyReq req) {
        AsyncReplyCommand command = Converter.INSTANCE.to(req);
        ScrmUserInfo userInfo = ScrmLoginUtil.getAssignUserFromHeader(request);
        command.setCorpId(userInfo.getCropId());
        command.setProjectId(userInfo.getProjectId());
        scrmAiChatService.asyncReply(command);
        return BizResponse.success();
    }

    /**
     * 流式请求AI回复
     */
    @PostMapping(value = "/streamReply",produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @ApiOperation("[2.16][杨佳润]客服工作台侧边栏流式请求AI回复")
    public Flux<StreamChatResp> streamReply(HttpServletRequest request,@RequestBody ReplyReq req) {
        ReplyCommand command = Converter.INSTANCE.to(req);
        ScrmUserInfo userInfo = ScrmLoginUtil.getAssignUserFromHeader(request);
        command.setProjectId(userInfo.getProjectId());
        return scrmAiChatService.streamReply(command)
                .map(Converter.INSTANCE::to);
    }

    @RequestMapping("/updateAcceptStatus")
    @ApiOperation("[2.16][邓加国]更新AI回复采纳状态")
    public BizResponse<Void> updateAcceptStatus(@RequestBody UpdateAcceptStatusReq req) {
        UpdateAcceptStatusQuery query = Converter.INSTANCE.to(req);
        scrmAiChatService.updateAcceptStatus(query);
        return BizResponse.success();
    }

    @RequestMapping("/pageQueryRecord")
    @ApiOperation("[2.16][邓加国]分页查询历史AI回复记录")
    public BizResponse<List<AiChatRecordResp>> pageQueryRecord(HttpServletRequest request,@RequestBody AiChatRecordPageReq req) {
        AiChatRecordPageQuery query = Converter.INSTANCE.to(req);
        ScrmUserInfo userInfo = ScrmLoginUtil.getAssignUserFromHeader(request);
        query.setCorpId(userInfo.getCropId());
        query.setProjectId(userInfo.getProjectId());
        List<AiChatRecordBo> list = scrmAiChatService.pageQueryRecord(query);
        return BizResponse.success(Converter.INSTANCE.toAiChatRecordResp(list));
    }

    @RequestMapping("/queryUnRead")
    @ApiOperation("[2.22][杨佳润]查询未读AI回复")
    public BizResponse<UnReadAiRecordResp> queryUnRead(@RequestBody OpUnReadAiRecordReq req) {



        return null;
    }

    @RequestMapping("/clearUnRead")
    @ApiOperation("[2.22][杨佳润]清空未读记录")
    public BizResponse<Void> clearUnRead(@RequestBody OpUnReadAiRecordReq req) {
        return null;
    }

}
