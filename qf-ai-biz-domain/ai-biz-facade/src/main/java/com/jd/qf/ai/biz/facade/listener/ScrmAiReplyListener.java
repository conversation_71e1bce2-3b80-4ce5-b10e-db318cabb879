package com.jd.qf.ai.biz.facade.listener;

import com.alibaba.fastjson.JSON;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jd.qf.ai.biz.core.api.chat.ScrmMqAiChatService;
import com.jd.qf.ai.biz.core.api.chat.bo.ProcessCsRecordCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * AI回复监听器
 * <AUTHOR>
 * @description
 * @date 2025/5/27
 */
@Slf4j
@Component
public class ScrmAiReplyListener implements MessageListener {

    @Autowired
    private ScrmMqAiChatService scrmMqAiChatService;

    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            solveMessage(message);
        }
    }

    private void solveMessage(Message message) {
        log.info("收到待AI回复的监听消息:{}", message.getText());
        scrmMqAiChatService.replyByListener(JSON.parseObject(message.getText(), ProcessCsRecordCommand.class));
    }
}
