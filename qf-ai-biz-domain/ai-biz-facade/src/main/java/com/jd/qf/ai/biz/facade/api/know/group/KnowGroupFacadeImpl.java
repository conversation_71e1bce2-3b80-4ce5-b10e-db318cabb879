package com.jd.qf.ai.biz.facade.api.know.group;

import com.jd.qf.ai.biz.api.knowledgebase.group.KnowGroupFacade;
import com.jd.qf.ai.biz.api.knowledgebase.group.dto.*;
import com.jd.qf.ai.biz.common.enums.GroupTypeEnum;
import com.jd.qf.ai.biz.core.api.know.group.KnowGroupService;
import com.jd.qf.ai.biz.core.api.know.group.bo.CreateKnowGroupBo;
import com.jd.qf.ai.biz.core.api.know.group.bo.KnowGroupBo;
import com.jd.qf.ai.biz.core.api.know.group.bo.UpdateKnowGroupBo;
import com.jd.qf.ai.server.common.pojo.resp.BizResponse;
import com.jdt.open.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库分组门面实现类
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
@Service
@Slf4j
public class KnowGroupFacadeImpl implements KnowGroupFacade {

    @Autowired
    private KnowGroupService knowGroupService;

    /**
     * 查询知识群组列表
     * @param req 查询请求对象，包含项目ID
     * @return 知识群组列表的响应对象
     */
    @Override
    public BizResponse<List<QueryKnowGroupListResp>> queryKnowGroupList(QueryKnowGroupListReq req) {
        List<KnowGroupBo> knowGroupBoList = knowGroupService.queryKnowGroupList(req.getProjectId());
        List<QueryKnowGroupListResp> respList = knowGroupBoList.stream()
                .map(bo -> {
                    QueryKnowGroupListResp resp = new QueryKnowGroupListResp();
                    BeanUtils.copyProperties(bo, resp);
                    return resp;
                })
                .collect(Collectors.toList());
        return BizResponse.success(respList);
    }

    /**
     * 创建知识分组
     * @param req 创建知识分组请求对象
     * @return 返回创建的知识分组ID
     */
    @Override
    public BizResponse<Integer> createKnowGroup(CreateKnowGroupReq req) {
        if(GroupTypeEnum.getByCode(req.getGroupType()) == null) {
            log.warn("分组类型不合法，添加失败！groupType: {}", req.getGroupType());
            return BizResponse.error("分组类型不合法，添加失败！");
        }
        CreateKnowGroupBo createKnowGroupBo = new CreateKnowGroupBo();
        BeanUtils.copyProperties(req, createKnowGroupBo);
        try {
            Integer result = knowGroupService.createKnowGroup(createKnowGroupBo);
            return BizResponse.success(result);
        } catch (BizException e) {
            log.warn("创建分组失败，失败原因: {}", e.getMessage());
            return BizResponse.error(e.getMessage());
        }
    }

    /**
     * 重命名知识库分组。
     * @param req 重命名请求对象，包含要重命名的分组的ID和新名称。
     * @return 业务响应，包含重命名结果。
     */
    @Override
    public BizResponse<Integer> renameGroup(RenameKnowGroupReq req) {
        UpdateKnowGroupBo updateKnowGroupBo = new UpdateKnowGroupBo();
        BeanUtils.copyProperties(req, updateKnowGroupBo);
        try {
            Integer result = knowGroupService.updateGroupName(updateKnowGroupBo);
            return BizResponse.success(result);
        } catch (BizException e) {
            log.warn("更新分组名称失败，失败原因: {}", e.getMessage());
            return BizResponse.error(e.getMessage());
        }
    }

    /**
     * 删除知识库分组
     * @param req 删除知识库分组请求
     * @return 删除操作的结果
     */
    @Override
    public BizResponse<Integer> deleteKnowGroup(DeleteKnowGroupReq req) {
        try {
            Integer result = knowGroupService.deleteKnowGroup(req.getProjectId(), req.getGroupNo(), req.getModifier());
            return BizResponse.success(result);
        } catch (BizException e) {
            log.warn("删除分组失败，失败原因为：{}", e.getMessage());
            return BizResponse.error(e.getMessage());
        }
    }
}
