package com.jd.qf.ai.biz.facade.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.jd.fastjson.JSONArray;
import com.jd.fastjson.JSONObject;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jd.qf.ai.biz.common.dto.CsChatRecordModel;
import com.jd.qf.ai.biz.common.enums.ChatTypeEnum;
import com.jd.qf.ai.biz.common.enums.FansTypeEnum;
import com.jd.qf.ai.biz.common.enums.WxMsgTypeEnum;
import com.jd.qf.ai.biz.core.api.chat.ScrmMqAiChatService;
import com.jd.qf.ai.biz.core.api.chat.bo.ProcessCsRecordCommand;
import com.jd.qf.ai.biz.facade.listener.dto.BinlogMessage;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.SysUserMapper;
import com.jd.qf.ai.biz.infrastructure.dao.po.SysUserPo;
import com.jdt.open.exception.BizException;
import com.jdt.open.exception.ResponseCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * cs_chat_record_new表binlog监听器
 * 只监听用户发的消息
 * <AUTHOR>
 * @date 2024/11/29 14:45
 */
@Slf4j
@Service
public class ScrmChatRecordBinlogListener implements MessageListener {

    @Autowired
    private ScrmMqAiChatService scrmMqAiChatService;
    @Autowired
    private SysUserMapper sysUserMapper;

    @Override
    public void onMessage(List<Message> list) throws Exception {

        for (Message message : list) {
            solveMessage(message);
        }
    }

    private void solveMessage(Message message) {
        try {
            String text = message.getText();
            if (StringUtils.isBlank(message.getText())) {
                return;
            }
            log.info("cs_chat_record_new表binlog监听器收到消息:{}", text);
            BinlogMessage messageDto = JSON.parseObject(text, BinlogMessage.class);
            if (Boolean.FALSE.equals(messageDto.isValidMessage())) {
                log.info("binlog不合法,不进行处理");
                return;
            }
            CsChatRecordModel csChatRecordModel = messageDto.getCsChatRecordModel();
            if (!checkMessage(csChatRecordModel)) {
                log.info("消息体不合法,不进行处理");
                return;
            }
            scrmMqAiChatService.processBinLog(csChatRecordModel);
        } catch (Exception e) {
            log.error("cs_chat_record_new表binlog监听器异常,抛出异常后开始重试", e);
            throw new BizException(ResponseCodeEnum.ERROR.getCode(), e.getMessage());
        }
    }

    private boolean checkMessage(CsChatRecordModel csChatRecordModel) {
        if (csChatRecordModel == null) {
            log.error("消息体为空");
            return false;
        }
        if (!StringUtils.equals(ChatTypeEnum.RECEIVE.getKey(), csChatRecordModel.getChatType())) {
            log.info("不是接收消息");
            return false;
        }
        if (StringUtils.isBlank(csChatRecordModel.getMsgType())) {
            log.info("消息类型为空");
            return false;
        }
        if (!StringUtils.equals(WxMsgTypeEnum.TEXT.getWxMsgType(), csChatRecordModel.getMsgType())
                && !StringUtils.equals(WxMsgTypeEnum.MIXTEXT.getWxMsgType(), csChatRecordModel.getMsgType())) {
            log.info("不是文本消息");
            return false;
        }

        //过滤一种特殊的场景,群聊场景下,员工也会收到一条消息,这条消息的发送者是员工自己,这种消息不需要处理
        if (FansTypeEnum.GROUP.getCode().equals(csChatRecordModel.getFansType())){
            List<SysUserPo> sysUserPos = sysUserMapper.queryByWxSysUserId(csChatRecordModel.getWxCustId());
            if (CollectionUtil.isNotEmpty(sysUserPos)){
                log.info("群聊场景下,员工也会收到一条消息,这条消息的发送者是员工自己,这种消息不需要处理");
                return false;
            }
        }
        return true;
    }
}
