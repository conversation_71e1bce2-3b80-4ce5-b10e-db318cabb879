<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.qf.ai.biz.infrastructure.dao.mapper.CsChatRecordNewMapper">
    <select id="queryList" resultType="com.jd.qf.ai.biz.infrastructure.dao.po.CsChatRecordPo">
        SELECT
        CASE WHEN chat_type = 'send' THEN send_time ELSE receive_time END as msgTime,
        id                                              as id,
        msg_id                                          as msgId,
        wx_cust_id                                      as wxCustId,
        msg_json                                        as msgJson,
        chat_type                                       as chatType,
        cust_id                                         as custId,
        wx_group_id                                     as groupId,
        fans_type                                       as fansType,
        robot_id                                        as robotId,
        recall                                          as isRecall,
        msg_send_status                                 as msgStatus,
        msg_type                                        as msgType
        FROM cs_chat_record_new
        <where>
            corp_id = #{corpId}
            and recall = 0
            <if test="sysUserId != null and sysUserId != ''">
                and robot_id = #{sysUserId}
            </if>
            <if test="custId != null and custId != ''">
                and cust_id = #{custId}
            </if>
            <if test="wxCustId != null and wxCustId != ''">
                and wx_cust_id = #{wxCustId}
            </if>
            <if test="startMsgTime != null">
                and (CASE WHEN chat_type = 'send' THEN send_time ELSE receive_time END) &gt;= #{startMsgTime}
            </if>
            <if test="endMsgTime != null">
                and (CASE WHEN chat_type = 'send' THEN send_time ELSE receive_time END) &lt;= #{endMsgTime}
            </if>
            <if test="excludeMsgId != null and excludeMsgId != ''">
                and id != #{excludeMsgId,jdbcType=INTEGER}
            </if>
            <if test="fansType != null and fansType != ''">
                and fans_type = #{fansType}
            </if>
            <if test="msgType != null and msgType != ''">
                and msg_type = #{msgType}
            </if>
            <if test="msgTypeList != null and msgTypeList.size() != 0">
                and msg_type IN
                <foreach item="item" index="index" collection="msgTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="groupId != null and groupId != ''">
                and wx_group_id = #{groupId}
            </if>
        </where>
        ORDER BY COALESCE(send_time, receive_time) DESC, id DESC
        limit #{limit,jdbcType=INTEGER}
    </select>
</mapper>