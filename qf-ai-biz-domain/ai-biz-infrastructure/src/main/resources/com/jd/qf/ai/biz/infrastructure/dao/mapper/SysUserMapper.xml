<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jd.qf.ai.biz.infrastructure.dao.mapper.SysUserMapper">

    <select id="querySysUserById" resultType="com.jd.qf.ai.biz.infrastructure.dao.po.SysUserPo">
        select id as sysUserId, user_name as sysUserName, project_id as projectId
        from sys_user
        where id = #{sysUserId,jdbcType=INTEGER};
    </select>

    <select id="queryByWxSysUserId" resultType="com.jd.qf.ai.biz.infrastructure.dao.po.SysUserPo">
        select id as sysUserId, user_name as sysUserName, project_id as projectId
        from sys_user
        where wx_sys_user_id = #{wxSysUserId,jdbcType=VARCHAR};
    </select>
</mapper>