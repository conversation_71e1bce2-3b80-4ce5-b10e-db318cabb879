package com.jd.qf.ai.biz.infrastructure.rpc.chat;

import com.alibaba.fastjson.JSON;
import com.jd.qf.ai.server.common.pojo.enums.IntentTypeEnum;
import com.jd.qf.ai.server.common.pojo.resp.BizResponse;
import com.jd.qf.ai.server.common.pojo.resp.CommonStreamResponse;
import com.jd.qf.ai.server.sdk.request.ChatReq;
import com.jd.qf.ai.server.sdk.request.GeneralIntentReq;
import com.jd.qf.ai.server.sdk.response.GeneralIntentResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
@Service
@Slf4j
public class AgentChatRpcServiceImpl implements AgentChatRpcService {

    /**
     * 调用AI基础服务的客户端
     */
    private final WebClient agentWebClient;

    public AgentChatRpcServiceImpl(@Value("${agent.api.base-url}") String routeBaseUrl) {
        this.agentWebClient = WebClient.builder()
                .baseUrl(routeBaseUrl)
                .build();
    }


    @Override
    public Flux<CommonStreamResponse> streamChat(ChatReq req) {
        return agentWebClient.post()
                .uri("/chat/streamChat")
                .accept(MediaType.TEXT_EVENT_STREAM)
                .contentType(MediaType.APPLICATION_JSON)
                .header("Accept-Charset", "UTF-8")
                .bodyValue(req)
                .retrieve()
                .bodyToFlux(CommonStreamResponse.class)
                .onErrorResume(e -> {
                    log.error("流式聊天RPC异常", e);
                    return Flux.empty();
                });
    }

    @Override
    public Mono<GeneralIntentResp> generalIntent(GeneralIntentReq req) {

        return agentWebClient.post()
                .uri("/intent/generalIntent")
                .bodyValue(req)
                .retrieve()
                .bodyToMono(new ParameterizedTypeReference<BizResponse<GeneralIntentResp>>() {
                })
                .map(resp -> {
                    if (resp == null || resp.getData() == null) {
                        log.error("意图识别调用失败, 响应结果: {}", JSON.toJSONString(resp));
                        return GeneralIntentResp.builder().intentType(IntentTypeEnum.NO_REPLY.getCode()).build();
                    }
                    log.info("意图识别返回结果为{}", JSON.toJSONString(resp));
                    return resp.getData();
                }).onErrorResume(e -> {
                    log.error("意图识别RPC异常", e);
                    return Mono.just(GeneralIntentResp.builder().intentType(IntentTypeEnum.NO_REPLY.getCode()).build());
                });
    }
}
