package com.jd.qf.ai.biz.infrastructure.dao.mapper;

import com.jd.qf.ai.biz.infrastructure.dao.po.ProjectPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.SysUserPo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 员工Mapper
 * <AUTHOR>
 * @description
 * @date 2025/5/8
 */
public interface SysUserMapper {

    SysUserPo querySysUserById(@Param("sysUserId") Integer sysUserId);

    List<SysUserPo> queryByWxSysUserId(@Param("wxSysUserId") String wxSysUserId);
}
