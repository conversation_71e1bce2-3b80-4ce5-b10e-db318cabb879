package com.jd.qf.ai.biz.infrastructure.dao.po;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 聊天记录表
 * cs_chat_record_new
 */
@Data
public class CsChatRecordQueryPo {

    /**
     * 消息ID
     */
    private Long excludeMsgId;

    /**
     * 消息开始时间
     */
    private Date startMsgTime;

    /**
     * 消息结束时间
     */
    private Date endMsgTime;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 员工ID
     */
    private Integer sysUserId;

    /**
     * 客户ID
     */
    private String custId;

    /**
     * 限制的消息条数
     */
    private Integer limit;

    /**
     * 聊天类型
     */
    private String fansType;

    /**
     * 消息类型
     */
    private String msgType;

    /**
     * 消息类型列表
     */
    private List<String> msgTypeList;

    /**
     * 群ID
     */
    private String groupId;

    /**
     * 微信客户ID
     */
    private String wxCustId;

}