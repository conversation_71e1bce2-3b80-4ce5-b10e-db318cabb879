package com.jd.qf.ai.biz.core.converter;

import com.jd.qf.ai.biz.core.api.chat.bo.*;
import com.jd.qf.ai.biz.core.api.know.qa.bo.EditQACommand;
import com.jd.qf.ai.biz.core.api.know.qa.bo.PageQAQuery;
import com.jd.qf.ai.biz.core.api.know.qa.bo.PageQueryQABo;
import com.jd.qf.ai.biz.core.api.know.qa.bo.QADetailBo;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiChatRecordQueryPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowQaPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.PageQueryQAPo;
import com.jd.qf.ai.server.common.pojo.resp.CommonStreamResponse;
import com.jd.qf.ai.server.sdk.response.GeneralIntentResp;
import org.mapstruct.Mapper;

import java.util.List;

import static org.mapstruct.factory.Mappers.getMapper;

@Mapper
public interface Converter {

    Converter INSTANCE = getMapper(Converter.class);

    AiChatRecordQueryPo to(AiChatRecordPageQuery query);

    AiChatRecordBo to(AiChatRecordPo po);

    IntentQuery toIntentQuery(ReplyCommand command);

    StreamChatBo to(CommonStreamResponse resp);

    IntentBo to(GeneralIntentResp resp);

    List<AiChatRecordBo> toAiBoList(List<AiChatRecordPo> list);

    IntentQuery toIntentQuery(ProcessCsRecordCommand command);

    ReplyCommand to(ProcessCsRecordCommand command);

    IntentQuery toIntentQuery(AsyncReplyCommand command);

    ReplyCommand to(AsyncReplyCommand command);

    QADetailBo to(AiKnowQaPo po);

    AiKnowQaPo to(EditQACommand req);

    PageQueryQAPo to(PageQAQuery req);

    PageQueryQABo to1(AiKnowQaPo aiKnowQaPo);
}
