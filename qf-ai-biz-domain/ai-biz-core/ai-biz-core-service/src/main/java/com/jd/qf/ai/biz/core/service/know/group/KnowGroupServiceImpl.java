package com.jd.qf.ai.biz.core.service.know.group;

import com.jd.qf.ai.biz.common.enums.GroupTypeEnum;
import com.jd.qf.ai.biz.core.api.know.group.KnowGroupService;
import com.jd.qf.ai.biz.core.api.know.group.bo.*;
import com.jd.qf.ai.biz.core.api.know.qa.KnowledgeBaseService;
import com.jd.qf.ai.biz.core.api.know.qa.bo.BatchDeleteQaCommand;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.AiKnowGroupMapper;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.ProjectMapper;
import com.jd.qf.ai.biz.infrastructure.dao.po.AiKnowGroupPo;
import com.jd.qf.ai.biz.infrastructure.dao.po.ProjectPo;
import com.jd.qf.ai.server.common.pojo.utils.SequenceNoUtils;
import com.jdt.open.exception.BizException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库分组服务
 * <AUTHOR>
 * @description
 * @date 2025/5/22
 */
@Slf4j
@Service
public class KnowGroupServiceImpl implements KnowGroupService {

    @Autowired
    private AiKnowGroupMapper aiKnowGroupMapper;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private KnowledgeBaseService knowledgeBaseService;

    /**
     * 根据项目ID查询知识组列表
     * @param projectId 项目ID
     * @return 知识组列表
     */
    @Override
    public List<KnowGroupBo> queryKnowGroupList(String projectId) {
        List<AiKnowGroupPo> aiKnowGroupPoList = aiKnowGroupMapper.selectByProjectId(projectId);
        return aiKnowGroupPoList.stream()
                .map(po -> {
                    KnowGroupBo bo = new KnowGroupBo();
                    BeanUtils.copyProperties(po, bo);
                    return bo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 创建知识图谱分组。
     * @param createKnowGroupBo 包含项目ID、分组名称、分组类型等信息的BO对象。
     * @return 新创建的分组的ID。
     * @throws BizException 如果项目不存在、同一项目下已存在相同名称的分组或该项目下已存在默认分组（如果当前创建的是默认分组），则抛出此异常。
     */
    @Override
    public Integer createKnowGroup(CreateKnowGroupBo createKnowGroupBo) throws BizException{
        ProjectPo projectPo = projectMapper.queryByProjectId(createKnowGroupBo.getProjectId());
        if(projectPo == null) {
            log.warn("添加失败，项目不存在！projectId: {}", createKnowGroupBo.getProjectId());
            throw new BizException("添加失败，项目不存在！");
        }
        // 检查同一项目下是否已存在相同名称的分组
        AiKnowGroupPo existingGroup = aiKnowGroupMapper.selectByProjectIdAndGroupName(createKnowGroupBo.getProjectId(), createKnowGroupBo.getGroupName());
        if (existingGroup != null) {
            log.warn("添加失败，同一项目下已存在相同名称的分组. projectId: {}, groupName: {}", createKnowGroupBo.getProjectId(), createKnowGroupBo.getGroupName());
            throw new BizException("添加失败，同一项目下已存在相同名称的分组");
        }

        // 检查是否已存在默认分组（如果当前创建的是默认分组）
        if (GroupTypeEnum.DEFAULT.getCode().equals(createKnowGroupBo.getGroupType())) {
            AiKnowGroupPo defaultGroup = aiKnowGroupMapper.selectDefaultGroupByProjectId(createKnowGroupBo.getProjectId());
            if (defaultGroup != null) {
                log.warn("添加失败，该项目下已存在默认分组. projectId: {}", createKnowGroupBo.getProjectId());
                throw new BizException("添加失败，该项目下已存在默认分组");
            }
        }

        // 创建新的分组记录
        AiKnowGroupPo newGroup = new AiKnowGroupPo();
        BeanUtils.copyProperties(createKnowGroupBo, newGroup);
        newGroup.setCreator(createKnowGroupBo.getModifier());
        newGroup.setModifier(createKnowGroupBo.getModifier());
        newGroup.setGroupNo(SequenceNoUtils.getSequenceNo()); // 生成分组编号
        newGroup.setProjectName(projectPo.getProjectName());
        return aiKnowGroupMapper.insert(newGroup);
    }

    /**
     * 更新分组名称
     * @param updateKnowGroupBo 更新知识群组的业务对象
     * @return 更新是否成功
     * @throws BizException 业务异常
     */
    @Override
    public Integer updateGroupName(UpdateKnowGroupBo updateKnowGroupBo) throws BizException {
        // 检查分组是否存在
        AiKnowGroupPo existingGroup = aiKnowGroupMapper.selectByGroupNo(updateKnowGroupBo.getProjectId(), updateKnowGroupBo.getGroupNo());
        if (existingGroup == null) {
            log.warn("更新失败，分组不存在. projectId: {}, groupNo: {}", updateKnowGroupBo.getProjectId(), updateKnowGroupBo.getGroupNo());
            throw new BizException("更新失败，分组不存在");
        }

        // 检查是否为默认分组
        if (GroupTypeEnum.DEFAULT.getCode().equals(existingGroup.getGroupType())) {
            log.warn("更新失败，默认分组名称不能修改. projectId: {}, groupNo: {}", updateKnowGroupBo.getProjectId(), updateKnowGroupBo.getGroupNo());
            throw new BizException("更新失败，默认分组名称不能修改");
        }

        // 检查新名称是否与其他分组重复
        AiKnowGroupPo groupWithSameName = aiKnowGroupMapper.selectByProjectIdAndGroupName(updateKnowGroupBo.getProjectId(), updateKnowGroupBo.getGroupName());
        if (groupWithSameName != null && !groupWithSameName.getGroupNo().equals(updateKnowGroupBo.getGroupNo())) {
            log.warn("更新失败，同一项目下已存在相同名称的分组. projectId: {}, groupName: {}", updateKnowGroupBo.getProjectId(), updateKnowGroupBo.getGroupName());
            throw new BizException("更新失败，同一项目下已存在相同名称的分组");
        }

        // 更新分组名称
        return aiKnowGroupMapper.updateGroupName(
                updateKnowGroupBo.getProjectId(), updateKnowGroupBo.getGroupNo(),
                updateKnowGroupBo.getGroupName(), updateKnowGroupBo.getModifier());
    }

    /**
     * 删除知识库分组
     * @param projectId 项目ID
     * @param groupNo 分组编号
     * @param modifier 更新人
     * @return 是否删除成功
     * @throws BizException 业务异常
     */
    @Override
    public Integer deleteKnowGroup(String projectId, String groupNo, String modifier) throws BizException {
        // 检查分组是否存在
        AiKnowGroupPo existingGroup = aiKnowGroupMapper.selectByGroupNo(projectId, groupNo);
        if (existingGroup == null) {
            log.warn("删除失败，分组不存在. projectId: {}, groupNo: {}", projectId, groupNo);
            throw new BizException("删除失败，分组不存在");
        }

        // 检查是否为默认分组
        if (GroupTypeEnum.DEFAULT.getCode().equals(existingGroup.getGroupType())) {
            log.warn("删除失败，默认分组不能删除. projectId: {}, groupNo: {}", projectId, groupNo);
            throw new BizException("删除失败，默认分组不能删除");
        }
        Integer result = aiKnowGroupMapper.deleteByPrimaryKey(existingGroup.getId(), modifier);
        knowledgeBaseService.batchDeleteQa(BatchDeleteQaCommand.builder().projectId(projectId).groupNo(groupNo).build());
        return result;
    }

}
