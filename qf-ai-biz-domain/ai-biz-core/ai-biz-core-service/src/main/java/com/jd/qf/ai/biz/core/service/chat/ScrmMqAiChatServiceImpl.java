package com.jd.qf.ai.biz.core.service.chat;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.jd.fastjson.JSONArray;
import com.jd.fastjson.JSONObject;
import com.jd.qf.ai.biz.common.constants.RedisPrefixConstants;
import com.jd.qf.ai.biz.common.dto.CsChatRecordModel;
import com.jd.qf.ai.biz.common.enums.WxMsgTypeEnum;
import com.jd.qf.ai.biz.core.api.chat.ScrmAiChatService;
import com.jd.qf.ai.biz.core.api.chat.ScrmMqAiChatService;
import com.jd.qf.ai.biz.core.api.chat.bo.*;
import com.jd.qf.ai.biz.core.converter.Converter;
import com.jd.qf.ai.biz.core.service.mq.MqProcessEngineer;
import com.jd.qf.ai.biz.infrastructure.dao.mapper.SysUserMapper;
import com.jd.qf.ai.biz.infrastructure.dao.po.SysUserPo;
import com.jd.qf.ai.server.common.pojo.enums.IntentTypeEnum;
import com.jdt.open.capability.client.redis.OpenRedisClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2025/5/27
 */
@Service
@Slf4j
public class ScrmMqAiChatServiceImpl implements ScrmMqAiChatService {

    @Autowired
    private ScrmAiChatService scrmAiChatService;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private MqProcessEngineer mqProcessEngineer;
    @Value("${jmq.topic.delayProcessAiReply}")
    private String delayProcessAiReplyTopic;
    @Autowired
    private OpenRedisClient openRedisClient;

    /**
     * 此方法负责将binlog消息预处理,然后通过MQ进行分发给业务处理器
     */
    @Override
    public void processBinLog(CsChatRecordModel model) {
        //过滤消息,构建命令
        ProcessCsRecordCommand command = buildCsChatCommand(model);

        //使用jmq实现的自发自收延迟队列,延迟的目的避免和前端请求冲突,保证一条消息实际只处理一次
        mqProcessEngineer.sendDelay(JSON.toJSONString(command), delayProcessAiReplyTopic, command.getMsgRecordId(), 500, TimeUnit.MILLISECONDS);

        //其他业务消息分发
    }

    private ProcessCsRecordCommand buildCsChatCommand(CsChatRecordModel csChatRecordModel) {
        ProcessCsRecordCommand command = new ProcessCsRecordCommand();
        command.setCustId(csChatRecordModel.getCustId());
        command.setCorpId(csChatRecordModel.getCorpId());
        command.setMsgRecordId(String.valueOf(csChatRecordModel.getId()));
        command.setMsgTime(csChatRecordModel.getReceiveTime() == null ? csChatRecordModel.getSendTime() : csChatRecordModel.getReceiveTime());
        command.setSysUserId(csChatRecordModel.getRobotId());
        command.setCustId(csChatRecordModel.getCustId());
        command.setGroupId(csChatRecordModel.getGroupId());
        command.setFansType(csChatRecordModel.getFansType());

        JSONObject jsonObject = com.jd.fastjson.JSON.parseObject(csChatRecordModel.getMsgJson());
        if (StringUtils.equals(WxMsgTypeEnum.TEXT.getWxMsgType(), csChatRecordModel.getMsgType())) {
            command.setContent(jsonObject.getString("content"));
        }
        if (StringUtils.equals(WxMsgTypeEnum.MIXTEXT.getWxMsgType(), csChatRecordModel.getMsgType())) {
            // 获取 "content" 对应的 JSON 数组
            JSONArray mixTextArray = jsonObject.getJSONArray(WxMsgTypeEnum.MIXTEXT.getWxMsgType());
            // 创建一个 StringBuilder 来存储所有 subtype 为 0 的文本
            StringBuilder contentBuilder = new StringBuilder();
            // 遍历 JSON 数组
            for (int i = 0; i < mixTextArray.size(); i++) {
                JSONObject contextObject = mixTextArray.getJSONObject(i);

                // 检查 subtype 是否为 0
                Integer subtype = contextObject.getInteger("subtype");
                if (subtype != null && subtype == 0) {
                    // 追加文本内容到 StringBuilder
                    String text = contextObject.getString(WxMsgTypeEnum.TEXT.getWxMsgType());
                    if (text != null) {
                        contentBuilder.append(text);
                    }
                }
            }
            // 将拼接的文本设置为命令的内容
            command.setContent(contentBuilder.toString());
        }
        return command;
    }

    @Override
    public void replyByListener(ProcessCsRecordCommand command) {

        String hasRequestByFronted = openRedisClient.get(RedisPrefixConstants.FRONTEND_REQUEST + command.getMsgRecordId());
        if (StrUtil.isNotBlank(hasRequestByFronted)) {
            log.info("通过监听器请求AI回复,发现Redis中存在前端请求的标识,不进行处理,消息ID:{}", command.getMsgRecordId());
            return;
        }

        //查询员工所在项目
        SysUserPo sysUserPo = sysUserMapper.querySysUserById(command.getSysUserId());
        if (sysUserPo == null || StrUtil.isBlank(sysUserPo.getProjectId())) {
            log.warn("通过监听器请求AI回复,未找到员工或项目信息,员工ID:{}", command.getSysUserId());
            return;
        }

        IntentQuery query = Converter.INSTANCE.toIntentQuery(command);
        query.setProjectId(sysUserPo.getProjectId());

        scrmAiChatService.commonIntent(query).doOnSuccess(intentBo -> {
                    if (IntentTypeEnum.REPLY.getCode().equals(intentBo.getIntentType())) {
                        //设置意图识别得到的上下文
                        command.setMessageList(intentBo.getMessageList());
                        ReplyCommand replyCommand = Converter.INSTANCE.to(command);
                        scrmAiChatService.streamReply(replyCommand).subscribe();
                    }
                })
                .subscribe();
    }
}
