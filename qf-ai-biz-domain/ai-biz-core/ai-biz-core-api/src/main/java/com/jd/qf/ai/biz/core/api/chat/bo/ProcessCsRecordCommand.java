package com.jd.qf.ai.biz.core.api.chat.bo;

import com.jd.qf.ai.server.common.pojo.dto.CommonMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 处理群控消息请求
 * <AUTHOR>
 * @description
 * @date 2025/5/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProcessCsRecordCommand {

    /**
     * 消息ID
     */
    private String msgRecordId;

    /**
     * 消息发送时间
     */
    private Date msgTime;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 企业ID
     */
    private String corpId;

    /**
     * 员工ID
     */
    private Integer sysUserId;

    /**
     * 客户ID
     */
    private String custId;

    /**
     * 消息内容
     */
    private List<CommonMessage> messageList;

    /**
     * 群聊ID
     */
    private String groupId;

    /**
     * 聊天形式:FANS-粉丝,GROUP-客户
     */
    private String fansType;

    /**
     * 客户微信ID
     */
    private String wxCustId;

}
