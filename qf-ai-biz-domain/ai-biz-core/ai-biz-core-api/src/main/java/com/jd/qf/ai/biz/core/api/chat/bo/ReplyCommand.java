package com.jd.qf.ai.biz.core.api.chat.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jd.qf.ai.server.common.pojo.dto.CommonMessage;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * AI回复请求
 * <AUTHOR>
 * @description
 * @date 2025/5/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReplyCommand {

    /**
     * 消息ID
     */
    private String msgRecordId;

    /**
     * 项目ID
     */
    private String projectId;

    /**
     * 消息内容
     */
    @Valid
    private List<CommonMessage> messageList;

    /**
     * 粉丝类型
     */
    private String fansType;

    /**
     * 群ID
     */
    private String groupId;

    /**
     * 客户ID
     */
    private String custId;
}
