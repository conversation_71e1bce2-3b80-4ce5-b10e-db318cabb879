package com.jd.qf.ai.biz.core.api.chat.bo;

import com.jd.qf.ai.server.common.pojo.dto.CommonMessage;
import com.jd.qf.ai.server.common.pojo.enums.IntentTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 意图识别返回值
 * <AUTHOR>
 * @description
 * @date 2025/5/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class IntentBo {

    /**
     * 识别结果:通过-PASS;不通过-FAIL
     * @see IntentTypeEnum
     */
    private String intentType;

    /**
     * 代理ID
     */
    private String agentId;

    /**
     * 消息内容
     */
    private List<CommonMessage> messageList;

    public IntentBo(String intentType) {
        this.intentType = intentType;
    }

}
