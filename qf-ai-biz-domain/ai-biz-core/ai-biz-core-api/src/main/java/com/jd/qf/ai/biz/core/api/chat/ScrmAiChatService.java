package com.jd.qf.ai.biz.core.api.chat;

import com.jd.qf.ai.biz.core.api.chat.bo.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.List;

public interface ScrmAiChatService {

    /**
     * 修改采纳状态
     * @param query
     */
    void updateAcceptStatus(UpdateAcceptStatusQuery query);

    /**
     * 分页查询AI聊天记录
     * @param query 包含分页参数和查询条件的对象
     * @return 包含分页结果和AI聊天记录业务对象的封装
     */
    List<AiChatRecordBo> pageQueryRecord(AiChatRecordPageQuery query);

    /**
     * 意图识别
     */
    Mono<IntentBo> intent(IntentQuery query);

    /**
     * 通用意图识别
     */
    Mono<IntentBo> commonIntent(IntentQuery query);

    /**
     * 异步请求AI回复
     */
    void asyncReply(AsyncReplyCommand command);

    /**
     * 流式请求AI回复
     */
    Flux<StreamChatBo> streamReply(ReplyCommand command);

    /**
     * 查询未读AI回复
     */
    UnReadAiRecordBo queryUnRead(OpUnReadAiRecordCommand req);

    /**
     * 清空未读AI回复
     */
    void clearUnRead(OpUnReadAiRecordCommand req);

}
