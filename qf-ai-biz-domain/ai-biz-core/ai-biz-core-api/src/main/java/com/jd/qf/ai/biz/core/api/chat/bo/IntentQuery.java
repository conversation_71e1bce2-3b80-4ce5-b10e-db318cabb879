package com.jd.qf.ai.biz.core.api.chat.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 意图识别请求
 * <AUTHOR>
 * @description
 * @date 2025/5/7
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class IntentQuery {

    /**
     * 消息ID
     */
    @NotBlank(message = "消息ID不能为空")
    private String msgRecordId;

    /**
     * 消息发送时间
     */
    @NotNull(message = "消息发送时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date msgTime;

    /**
     * 消息内容
     */
    @NotBlank(message = "消息内容不能为空")
    private String content;

    /**
     * 企业ID
     */
    @NotBlank(message = "企业ID不能为空")
    private String corpId;

    /**
     * 项目ID
     */
    @NotBlank(message = "项目ID不能为空")
    private String projectId;

    /**
     * 员工ID
     */
    @NotBlank(message = "员工ID不能为空")
    private Integer sysUserId;

    /**
     * 客户ID
     */
    @NotBlank(message = "客户ID不能为空")
    private String custId;

    /**
     * 客户名称
     */
    @NotBlank(message = "客户名称不能为空")
    private String custName;

    /**
     * 粉丝类型
     */
    private String fansType;

    /**
     * 群ID
     */
    private String groupId;

    /**
     * 客户微信ID
     */
    private String wxCustId;

    /**
     * 请求方式 前端-FRONTEND 后端MQ-BACKEND_MQ
     */
    private String requestType;


}
