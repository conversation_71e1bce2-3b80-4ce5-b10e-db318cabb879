package com.jd.qf.ai.biz.core.api.chat.bo;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 操作未读AI回复请求
 * <AUTHOR>
 * @description
 * @date 2025/5/21
 */
@Data
public class OpUnReadAiRecordCommand {

    /**
     * 客户ID
     */
    private String custId;

    /**
     * 群ID
     */
    private String groupId;

    /**
     * 粉丝类型
     */
    @NotBlank(message = "粉丝类型不能为空")
    private String fansType;
}
