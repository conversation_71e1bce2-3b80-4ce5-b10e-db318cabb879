package com.jd.qf.ai.biz.core.api.chat;

import com.jd.qf.ai.biz.common.dto.CsChatRecordModel;
import com.jd.qf.ai.biz.core.api.chat.bo.*;

/**
 * 针对MQ的AI聊天服务
 * <AUTHOR>
 * @description
 * @date 2025/5/27
 */
public interface ScrmMqAiChatService {

    /**
     * 监听群控消息的binlog,转换为处理请求
     * 通过MQ的方式进行分发
     */
    void processBinLog(CsChatRecordModel model);

    /**
     * 通过监听器请求AI回复
     */
    void replyByListener(ProcessCsRecordCommand command);
}
