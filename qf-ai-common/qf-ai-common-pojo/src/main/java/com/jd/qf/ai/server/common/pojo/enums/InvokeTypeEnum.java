package com.jd.qf.ai.server.common.pojo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 去重策略枚举
 * <AUTHOR>
 * @description
 * @date 2025/1/15
 */
@Getter
@AllArgsConstructor
public enum InvokeTypeEnum {

    /**
     * 流式
     */
    STREAM("STREAM", "流式"),

    /**
     * 阻塞调用
     */
    INVOKE("INVOKE", "阻塞调用")
    ;

    /**
     * 类型code
     */
    private final String code;
    /**
     * msg
     */
    private final String msg;

    public static InvokeTypeEnum getByCode(String code) {
        for (InvokeTypeEnum deduplicateEnum : InvokeTypeEnum.values()) {
            if (deduplicateEnum.getCode().equals(code)) {
                return deduplicateEnum;
            }
        }
        return null;
    }

    public static String getCodeByMsg(String msg) {
        for (InvokeTypeEnum deduplicateEnum : InvokeTypeEnum.values()) {
            if (deduplicateEnum.getMsg().equals(msg)) {
                return deduplicateEnum.getCode();
            }
        }
        return null;
    }

    public static String getMsgByCode(String code) {
        for (InvokeTypeEnum deduplicateEnum : InvokeTypeEnum.values()) {
            if (deduplicateEnum.getCode().equals(code)) {
                return deduplicateEnum.getMsg();
            }
        }
        return null;
    }
}
