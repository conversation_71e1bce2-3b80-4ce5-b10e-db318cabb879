aks:
  aliasName: pto_05_001
  appId: Test_Online
  appName: Test_Online
  jsfAlias: AKS-JSF-beta_jsf_gn
  key:
    alias:
      name: ${aks.aliasName}

pro:
  jimdb:
    url: jim://2914173422341158041/110000259
jdd:
  easyjob:
    appId: qf-ai-server
    enable: true
    host: http://schedule.jdd-beta.local:8080
    secret: 455d46c86d691bed1a72f2073be05e3e
jsf:
  registry:
    index: test.i.jsf.jd.local
  provider:
    alias: dev
  alias:
    jr-custinfo-api-service-exp: test


jmq:
  bizDomain:
    address: test-nameserver.jmq.jd.local:50088
    app: qfaiserver
    user: qfaiserver
    password: d5273212a770480d9ad537e74c51f5b5
  topic:
    csChatBinlog: cs_chat_record_new_binlog_test
    delayProcessAiReply: delayProcessAiReply_test

jmq2:
  deliverDomain:
    address: jmq-testcluster.jd.local:50088
    app: bpompSettle
    user: bpompSettle
    password: C653F323

ducc:
  appname: jdos_qf-ai-server
  configuration: test
  domain: test.ducc.jd.local
  namespace: qf_ai_server
  profiles: DUCC_DEFAULT_PROFILE
  token: 3afc91cf4ea74ef9871e33edc3b9229b
laf:
  config:
    logger:
      enabled: true
      key: logger.level
      type: logback
    manager:
      application: qf_ai_server
      parameters:
        - name: autoListener
          value: true
      resources:
        - name: qf_ai_server
          uri: ucc://${ducc.appname}:${ducc.token}@${ducc.domain}/v1/namespace/${ducc.namespace}/config/${ducc.configuration}/profiles/${ducc.profiles}?longPolling=15000&amp;necessary=true
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
open:
  act:
    log:
      app: qf-ai-server
      business: qf-ai-server
      enable: true
  lab:
    env: test
  mybatis:
    encrypt:
      enable: true
oss:
  accessKey: FEC6758F6B5C4D199B34CB894B7109EA
  bucket: bpomp
  endpoint: s3-internal.guan-test-1.jdd-office-beta.local
  export:
    temp:
      path: /export/temp
  secretKey: B8DF0D4F3FCF4AA08268B476E31699D5
  signingRegion: guan-test-1
spring:
  datasource:
    url: *****************************************************************************************************************************************************************************************************************************************************
    username: c_scrmzJXNyEhXsz

  main:
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 25MB
      max-request-size: 25MB

dify:
  api:
    base-url: http://**************/v1
    timeout: 30000

chatRhino:
  api:
    base-url: http://api.chatrhino.jd.com/api/v2


llm:
  sec:
    url: http://test.easymock.jd.com/llmsec/api/defense/v2/
    accessKey: JDR_weidianyun_001
    secretKey: fd52bdd3cac84308afcafd829831e2ee

jdq:
  producerConfigs:
    ai_agent_input_output:
      user: P8cdeb189
      appDomain: ENTRY
      password: vW9qwz2XsCpha7Jb

#jes配置,开发用的wdy-cdp
#jes配置
jes:
  serverUrl: http://es-abnep02i23-http.tpaas-es.svc.guan-test01.guan.x.abchost.local:9200
  appName: wdy-cdp
  secret: d5e88a9ca5ad7242e6e8af9f7be0967b
es:
  csChatIndex: dev_cs_chat_record_new

selfBuild:
  api:
    route:
      base-url: http://localhost:8000
    chat:
      base-url: http://localhost:8000

agent:
  api:
    base-url: http://localhost:8080/qf/ai/inner