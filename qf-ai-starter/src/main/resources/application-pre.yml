aks:
  aliasName: qFi_05_001
  appId: qf-ai-server
  appName: qf-ai-server
  jsfAlias: AKS-JSF
  key:
    alias:
      name: ${aks.aliasName}

pro:
  jimdb:
    url: jim://3005241126484935989/8410
jdd:
  easyjob:
    appId: qf-ai-server
    enable: true
    host: http://schedule.jdfin.local
    secret: 28fc75f605979238fa40b96e138045c1

jsf:
  registry:
    index: i.jsf.jd.com
  provider:
    alias: pre
  alias:
    jr-custinfo-api-service-exp: pro

jmq:
  bizDomain:
    address: nameserver.jmq.jd.local:80
    app: qfaiserver
    user: qfaiserver
    password: 0c9125ad52284aacbf22f89df92b92cd
  topic:
    csChatBinlog: cs_chat_record_new_binlog_pre
    delayProcessAiReply: delayProcessAiReply_pre


ducc:
  appname: jdos_qf-ai-server
  configuration: pre
  domain: ducc.jd.local
  namespace: qf_ai_server
  profiles: DUCC_DEFAULT_PROFILE
  token: 646dd9da64284b37beafc23c5382f2d2
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
laf:
  config:
    logger:
      enabled: true
      key: logger.level
      type: logback
    manager:
      application: qf_ai_server
      parameters:
        - name: autoListener
          value: true
      resources:
        - name: qf_ai_server
          uri: ucc://${ducc.appname}:${ducc.token}@${ducc.domain}/v1/namespace/${ducc.namespace}/config/${ducc.configuration}/profiles/${ducc.profiles}?longPolling=15000&amp;necessary=true
open:
  act:
    log:
      app: qf-ai-server-pre
      enable: true
  lab:
    env: pre
    app: qf-ai-server-pre

  mybatis:
    encrypt:
      enable: true
oss:
  accessKey: LOCAL_OSS_E640689DA7E388A93EE631
  bucket: bpomp-pre
  endpoint: s3.jdpay.com
  export:
    temp:
      path: /export/temp
  secretKey: E865C0F61D8D4DF884384E89B50D40C0
  signingRegion: tech-north-1
spring:
  datasource:
    url: **********************************************************************************************************************************************************************************************************************************************************
    username: mkt_scrm_pre
    hikari:
      connection-init-sql: SET SESSION group_concat_max_len = 1000000
  main:
    allow-circular-references: true
  servlet:
    multipart:
      max-file-size: 25MB
      max-request-size: 25MB

dify:
  api:
    base-url: http://**************/v1
    timeout: 30000

chatRhino:
  api:
    base-url: http://api.chatrhino.jd.com/api/v2

llm:
  sec:
    url: http://pre-llmsec-entry.jd.local/llmsec/api/defense/v2/
    accessKey: JDR_weidianyun_001
    secretKey: fd52bdd3cac84308afcafd829831e2ee

jdq:
  producerConfigs:
    ai_agent_input_output:
      user: P8cdeb189
      appDomain: ENTRY
      password: vW9qwz2XsCpha7Jb

jes:
  serverUrl: http://es-b8g7p5wl8q-http.es-b8g7p5wl8q.svc.guan-prod.gnhk.x.abchost.local:9200
  appName: wdy-cdp
  secret: 8bf858a8bddce1149c66c869e7316735
es:
  csChatIndex: pre_cs_chat_record_new

selfBuild:
  api:
    route:
      base-url: http://************:8000
    chat:
      base-url: http://************:8000

agent:
  api:
    base-url: http://localhost:8080/qf/ai/inner